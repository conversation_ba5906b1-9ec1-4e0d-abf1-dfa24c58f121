# Google Sheets 簡易資料庫建立指南

## 🎯 目標
為 workdays 專案建立 Google Sheets 作為簡易資料庫

## 📊 步驟 1：建立 Google Sheets

### **1.1 建立新試算表**
1. 前往 https://sheets.google.com
2. 點擊「建立」→「空白試算表」
3. 重新命名為：`workdays-database`

### **1.2 建立工作表結構**

#### **工作表 1：Users（用戶資料）**
重新命名第一個工作表為 `Users`，設定標題行：

| A | B | C | D | E | F | G | H | I | J |
|---|---|---|---|---|---|---|---|---|---|
| id | name | email | password | role | location | approved | monthlyLeaveDays | overtimeBalance | weeklySchedule |

#### **工作表 2：ScheduleRequests（排班申請）**
新增工作表 `ScheduleRequests`，設定標題行：

| A | B | C | D | E | F | G | H |
|---|---|---|---|---|---|---|---|
| id | employeeId | employeeName | month | status | scheduleData | submittedAt | approver |

#### **工作表 3：OvertimeRecords（額外班記錄）**
新增工作表 `OvertimeRecords`，設定標題行：

| A | B | C | D | E | F | G |
|---|---|---|---|---|---|---|
| id | employeeId | employeeName | date | duration | addedBy | addedDate |

#### **工作表 4：SystemSettings（系統設定）**
新增工作表 `SystemSettings`，設定標題行：

| A | B | C |
|---|---|---|
| settingKey | settingValue | updatedAt |

### **1.3 新增範例資料**

#### **Users 工作表範例資料**
```
id          | name      | email                    | password | role       | location | approved | monthlyLeaveDays | overtimeBalance | weeklySchedule
admin_001   | 管理員    | <EMAIL>        | admin123 | admin      | ciguang  | TRUE     | 0                | 0               | {}
super_001   | 主管      | <EMAIL>   | super123 | supervisor | ciguang  | TRUE     | 8                | 0               | {}
emp_001     | 員工      | <EMAIL>     | emp123   | employee   | ruiguang | TRUE     | 10               | 0               | {}
```

#### **SystemSettings 工作表範例資料**
```
settingKey      | settingValue                                    | updatedAt
companyName     | 工作排班管理系統                                | 2024-12-01T00:00:00Z
workLocations   | [{"id":"ciguang","name":"慈光"},{"id":"ruiguang","name":"瑞光"}] | 2024-12-01T00:00:00Z
```

### **1.4 取得 Sheets ID**
1. 複製瀏覽器網址列中的 Sheets ID
2. 格式：`https://docs.google.com/spreadsheets/d/SHEETS_ID/edit`
3. 記錄 SHEETS_ID 供後續使用

### **1.5 設定共享權限**
1. 點擊右上角「共用」
2. 設定為「知道連結的任何人都可以檢視」
3. 或設定特定使用者為「編輯者」

## ✅ 完成確認
- [ ] 已建立 4 個工作表
- [ ] 每個工作表都有正確的標題行
- [ ] 已新增範例資料
- [ ] 已取得 Sheets ID
- [ ] 已設定適當的共享權限

完成後請記錄您的 **Sheets ID**，下一步將建立 Google Apps Script API。
