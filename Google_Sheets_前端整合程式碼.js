/**
 * workdays 專案 - Google Sheets 前端整合程式碼
 * 將此程式碼加入到 schedule-app-simple.html 中
 */

// ==================== Google Sheets API 配置 ====================

// Google Apps Script Web App URL（請替換為您的實際 URL）
const GOOGLE_APPS_SCRIPT_URL = 'YOUR_GOOGLE_APPS_SCRIPT_URL_HERE';

// API 呼叫類別
class GoogleSheetsAPI {
    constructor(scriptUrl) {
        this.scriptUrl = scriptUrl;
    }

    /**
     * 發送 GET 請求
     */
    async get(action, params = {}) {
        try {
            const url = new URL(this.scriptUrl);
            url.searchParams.append('action', action);
            
            Object.keys(params).forEach(key => {
                url.searchParams.append(key, params[key]);
            });

            const response = await fetch(url.toString());
            const result = await response.json();
            
            if (!result.success) {
                throw new Error(result.data.error || 'API request failed');
            }
            
            return result.data;
        } catch (error) {
            console.error('GET request failed:', error);
            throw error;
        }
    }

    /**
     * 發送 POST 請求
     */
    async post(action, payload) {
        try {
            const response = await fetch(this.scriptUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: action,
                    payload: payload
                })
            });

            const result = await response.json();
            
            if (!result.success) {
                throw new Error(result.data.error || 'API request failed');
            }
            
            return result.data;
        } catch (error) {
            console.error('POST request failed:', error);
            throw error;
        }
    }

    // ==================== 具體 API 方法 ====================

    /**
     * 健康檢查
     */
    async healthCheck() {
        return await this.get('health');
    }

    /**
     * 用戶登入
     */
    async login(email, password) {
        return await this.post('login', { email, password });
    }

    /**
     * 獲取用戶列表
     */
    async getUsers() {
        return await this.get('getUsers');
    }

    /**
     * 建立新用戶
     */
    async createUser(userData) {
        return await this.post('createUser', userData);
    }

    /**
     * 獲取排班申請
     */
    async getScheduleRequests() {
        return await this.get('getScheduleRequests');
    }

    /**
     * 建立排班申請
     */
    async createScheduleRequest(requestData) {
        return await this.post('createScheduleRequest', requestData);
    }

    /**
     * 更新排班申請
     */
    async updateScheduleRequest(updateData) {
        return await this.post('updateScheduleRequest', updateData);
    }

    /**
     * 建立額外班記錄
     */
    async createOvertimeRecord(recordData) {
        return await this.post('createOvertimeRecord', recordData);
    }

    /**
     * 獲取系統設定
     */
    async getSystemSettings() {
        return await this.get('getSystemSettings');
    }
}

// 建立全域 API 實例
const sheetsAPI = new GoogleSheetsAPI(GOOGLE_APPS_SCRIPT_URL);

// ==================== 替換原有的資料函數 ====================

/**
 * 替換原有的 loadSystemData 函數
 */
async function loadSystemDataFromSheets() {
    try {
        showLoading('載入系統資料...');
        
        // 並行載入所有資料
        const [users, scheduleRequests, systemSettings] = await Promise.all([
            sheetsAPI.getUsers(),
            sheetsAPI.getScheduleRequests(), 
            sheetsAPI.getSystemSettings()
        ]);

        // 更新全域 systemData
        systemData.users = users;
        systemData.scheduleRequests = scheduleRequests;
        systemData.settings = systemSettings;
        
        // 同步到 localStorage 作為快取
        localStorage.setItem('workdaysSystemData', JSON.stringify(systemData));
        
        hideLoading();
        console.log('✅ 系統資料載入完成');
        
    } catch (error) {
        hideLoading();
        console.error('❌ 載入系統資料失敗:', error);
        
        // 嘗試從 localStorage 載入快取資料
        const cachedData = localStorage.getItem('workdaysSystemData');
        if (cachedData) {
            systemData = JSON.parse(cachedData);
            showNotification('使用快取資料，部分功能可能受限', 'warning');
        } else {
            showNotification('無法載入系統資料，請檢查網路連線', 'error');
        }
    }
}

/**
 * 替換原有的登入函數
 */
async function handleLoginWithSheets(event) {
    event.preventDefault();
    
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const loginButton = document.getElementById('login-button');
    const loginError = document.getElementById('login-error');
    
    // 重置錯誤狀態
    loginError.classList.add('hidden');
    loginButton.disabled = true;
    loginButton.textContent = '登入中...';
    
    try {
        // 使用 Google Sheets API 驗證
        const result = await sheetsAPI.login(email, password);
        
        if (result.success) {
            currentUser = result.user;
            localStorage.setItem('currentUser', JSON.stringify(currentUser));
            
            // 載入完整系統資料
            await loadSystemDataFromSheets();
            
            showDashboard();
            showNotification('登入成功！', 'success');
        }
        
    } catch (error) {
        console.error('登入失敗:', error);
        loginError.classList.remove('hidden');
        document.getElementById('login-error-text').textContent = 
            error.message || '登入失敗，請檢查帳號密碼';
    } finally {
        loginButton.disabled = false;
        loginButton.textContent = '登入';
    }
}

/**
 * 替換原有的排班申請提交函數
 */
async function submitScheduleRequestToSheets(requestData) {
    try {
        showLoading('提交排班申請...');
        
        const result = await sheetsAPI.createScheduleRequest({
            employeeId: currentUser.id,
            employeeName: currentUser.name,
            month: requestData.month,
            scheduleData: requestData.scheduleData
        });
        
        if (result.success) {
            // 重新載入排班申請資料
            systemData.scheduleRequests = await sheetsAPI.getScheduleRequests();
            
            hideLoading();
            showNotification('排班申請提交成功！', 'success');
            return true;
        }
        
    } catch (error) {
        hideLoading();
        console.error('提交排班申請失敗:', error);
        showNotification('提交失敗：' + error.message, 'error');
        return false;
    }
}

/**
 * 替換原有的審核函數
 */
async function approveScheduleRequestInSheets(requestId, status, approver) {
    try {
        showLoading('處理審核...');
        
        const result = await sheetsAPI.updateScheduleRequest({
            id: requestId,
            status: status,
            approver: approver
        });
        
        if (result.success) {
            // 重新載入排班申請資料
            systemData.scheduleRequests = await sheetsAPI.getScheduleRequests();
            
            hideLoading();
            showNotification('審核完成！', 'success');
            return true;
        }
        
    } catch (error) {
        hideLoading();
        console.error('審核失敗:', error);
        showNotification('審核失敗：' + error.message, 'error');
        return false;
    }
}

/**
 * 替換原有的額外班記錄函數
 */
async function addOvertimeRecordToSheets(recordData) {
    try {
        showLoading('新增額外班記錄...');
        
        const result = await sheetsAPI.createOvertimeRecord(recordData);
        
        if (result.success) {
            // 重新載入用戶資料以更新餘額
            systemData.users = await sheetsAPI.getUsers();
            
            hideLoading();
            showNotification('額外班記錄新增成功！', 'success');
            return true;
        }
        
    } catch (error) {
        hideLoading();
        console.error('新增額外班記錄失敗:', error);
        showNotification('新增失敗：' + error.message, 'error');
        return false;
    }
}

// ==================== 輔助函數 ====================

/**
 * 顯示載入狀態
 */
function showLoading(message = '載入中...') {
    // 建立或更新載入遮罩
    let loadingOverlay = document.getElementById('loading-overlay');
    if (!loadingOverlay) {
        loadingOverlay = document.createElement('div');
        loadingOverlay.id = 'loading-overlay';
        loadingOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        loadingOverlay.innerHTML = `
            <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
                <span id="loading-message" class="text-gray-700">${message}</span>
            </div>
        `;
        document.body.appendChild(loadingOverlay);
    } else {
        document.getElementById('loading-message').textContent = message;
        loadingOverlay.classList.remove('hidden');
    }
}

/**
 * 隱藏載入狀態
 */
function hideLoading() {
    const loadingOverlay = document.getElementById('loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.classList.add('hidden');
    }
}

/**
 * 顯示通知
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        type === 'warning' ? 'bg-yellow-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // 3秒後自動移除
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

/**
 * 測試 Google Sheets 連接
 */
async function testSheetsConnection() {
    try {
        showLoading('測試連接...');
        const result = await sheetsAPI.healthCheck();
        hideLoading();
        
        console.log('✅ Google Sheets 連接測試成功:', result);
        showNotification('Google Sheets 連接正常！', 'success');
        return true;
        
    } catch (error) {
        hideLoading();
        console.error('❌ Google Sheets 連接測試失敗:', error);
        showNotification('Google Sheets 連接失敗：' + error.message, 'error');
        return false;
    }
}

// ==================== 初始化 ====================

/**
 * 修改原有的 init 函數
 */
function initWithSheets() {
    lucide.createIcons();
    
    // 測試 Google Sheets 連接
    testSheetsConnection();
    
    // 載入系統資料
    loadSystemDataFromSheets();
    
    // 更新註冊區域內容
    updateRegistrationSection();
    
    // 檢查是否有儲存的登入狀態
    const savedUser = localStorage.getItem('currentUser');
    if (savedUser) {
        currentUser = JSON.parse(savedUser);
        showDashboard();
    }
}

// 在頁面載入完成後初始化
document.addEventListener('DOMContentLoaded', initWithSheets);
