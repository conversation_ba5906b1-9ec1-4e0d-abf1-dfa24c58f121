/**
 * workdays 專案 - 簡化版 Google Apps Script API
 * 專為 Google 協作平台部署設計
 */

// ==================== 配置設定 ====================
const SPREADSHEET_ID = 'YOUR_SHEETS_ID_HERE'; // 請替換為您的 Sheets ID

// 工作表名稱
const SHEETS = {
  USERS: 'Users',
  SCHEDULE_REQUESTS: 'ScheduleRequests', 
  OVERTIME_RECORDS: 'OvertimeRecords',
  SYSTEM_SETTINGS: 'SystemSettings'
};

// ==================== 主要 API 函數 ====================

/**
 * 處理 GET 請求
 */
function doGet(e) {
  const action = e.parameter.action || 'health';
  
  try {
    switch (action) {
      case 'health':
        return createResponse({ status: 'healthy', timestamp: new Date() });
      case 'getUsers':
        return createResponse(getUsers());
      case 'getScheduleRequests':
        return createResponse(getScheduleRequests());
      case 'getSystemSettings':
        return createResponse(getSystemSettings());
      default:
        return createResponse({ error: 'Unknown action' }, false);
    }
  } catch (error) {
    return createResponse({ error: error.message }, false);
  }
}

/**
 * 處理 POST 請求
 */
function doPost(e) {
  try {
    if (!e.postData || !e.postData.contents) {
      return createResponse({ error: 'No data provided' }, false);
    }
    
    const data = JSON.parse(e.postData.contents);
    const { action, payload } = data;
    
    switch (action) {
      case 'login':
        return createResponse(authenticateUser(payload));
      case 'createUser':
        return createResponse(createUser(payload));
      case 'createScheduleRequest':
        return createResponse(createScheduleRequest(payload));
      case 'updateScheduleRequest':
        return createResponse(updateScheduleRequest(payload));
      case 'createOvertimeRecord':
        return createResponse(createOvertimeRecord(payload));
      default:
        return createResponse({ error: 'Unknown action' }, false);
    }
  } catch (error) {
    return createResponse({ error: error.message }, false);
  }
}

// ==================== 資料操作函數 ====================

/**
 * 獲取用戶列表
 */
function getUsers() {
  const sheet = getSheet(SHEETS.USERS);
  const data = sheet.getDataRange().getValues();
  
  if (data.length <= 1) return [];
  
  const headers = data[0];
  const users = [];
  
  for (let i = 1; i < data.length; i++) {
    const user = {};
    headers.forEach((header, index) => {
      user[header] = data[i][index];
    });
    // 移除密碼欄位
    delete user.password;
    users.push(user);
  }
  
  return users;
}

/**
 * 用戶認證
 */
function authenticateUser(credentials) {
  const { email, password } = credentials;
  const sheet = getSheet(SHEETS.USERS);
  const data = sheet.getDataRange().getValues();
  
  if (data.length <= 1) {
    throw new Error('No users found');
  }
  
  const headers = data[0];
  const emailIndex = headers.indexOf('email');
  const passwordIndex = headers.indexOf('password');
  
  for (let i = 1; i < data.length; i++) {
    if (data[i][emailIndex] === email && data[i][passwordIndex] === password) {
      const user = {};
      headers.forEach((header, index) => {
        user[header] = data[i][index];
      });
      // 移除密碼
      delete user.password;
      return { success: true, user: user };
    }
  }
  
  throw new Error('Invalid credentials');
}

/**
 * 建立新用戶
 */
function createUser(userData) {
  const sheet = getSheet(SHEETS.USERS);
  const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
  
  // 生成新 ID
  const newId = 'user_' + Date.now();
  
  // 準備資料行
  const newRow = headers.map(header => {
    switch (header) {
      case 'id': return newId;
      case 'approved': return true;
      case 'monthlyLeaveDays': return userData.role === 'employee' ? 10 : 8;
      case 'overtimeBalance': return 0;
      case 'weeklySchedule': return '{}';
      default: return userData[header] || '';
    }
  });
  
  sheet.appendRow(newRow);
  return { success: true, id: newId };
}

/**
 * 獲取排班申請
 */
function getScheduleRequests() {
  const sheet = getSheet(SHEETS.SCHEDULE_REQUESTS);
  const data = sheet.getDataRange().getValues();
  
  if (data.length <= 1) return [];
  
  const headers = data[0];
  const requests = [];
  
  for (let i = 1; i < data.length; i++) {
    const request = {};
    headers.forEach((header, index) => {
      request[header] = data[i][index];
    });
    requests.push(request);
  }
  
  return requests;
}

/**
 * 建立排班申請
 */
function createScheduleRequest(requestData) {
  const sheet = getSheet(SHEETS.SCHEDULE_REQUESTS);
  const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
  
  const newId = 'req_' + Date.now();
  
  const newRow = headers.map(header => {
    switch (header) {
      case 'id': return newId;
      case 'status': return 'pending';
      case 'submittedAt': return new Date().toISOString();
      case 'scheduleData': return JSON.stringify(requestData.scheduleData || {});
      default: return requestData[header] || '';
    }
  });
  
  sheet.appendRow(newRow);
  return { success: true, id: newId };
}

/**
 * 更新排班申請
 */
function updateScheduleRequest(updateData) {
  const { id, status, approver } = updateData;
  const sheet = getSheet(SHEETS.SCHEDULE_REQUESTS);
  const data = sheet.getDataRange().getValues();
  
  const headers = data[0];
  const idIndex = headers.indexOf('id');
  const statusIndex = headers.indexOf('status');
  const approverIndex = headers.indexOf('approver');
  
  for (let i = 1; i < data.length; i++) {
    if (data[i][idIndex] === id) {
      if (statusIndex >= 0) sheet.getRange(i + 1, statusIndex + 1).setValue(status);
      if (approverIndex >= 0) sheet.getRange(i + 1, approverIndex + 1).setValue(approver);
      return { success: true };
    }
  }
  
  throw new Error('Request not found');
}

/**
 * 建立額外班記錄
 */
function createOvertimeRecord(recordData) {
  const sheet = getSheet(SHEETS.OVERTIME_RECORDS);
  const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
  
  const newId = 'ot_' + Date.now();
  
  const newRow = headers.map(header => {
    switch (header) {
      case 'id': return newId;
      case 'addedDate': return new Date().toISOString();
      default: return recordData[header] || '';
    }
  });
  
  sheet.appendRow(newRow);
  
  // 更新用戶的額外班餘額
  updateUserOvertimeBalance(recordData.employeeId, recordData.duration || 1);
  
  return { success: true, id: newId };
}

/**
 * 更新用戶額外班餘額
 */
function updateUserOvertimeBalance(userId, amount) {
  const sheet = getSheet(SHEETS.USERS);
  const data = sheet.getDataRange().getValues();
  
  const headers = data[0];
  const idIndex = headers.indexOf('id');
  const balanceIndex = headers.indexOf('overtimeBalance');
  
  for (let i = 1; i < data.length; i++) {
    if (data[i][idIndex] === userId) {
      const currentBalance = parseFloat(data[i][balanceIndex]) || 0;
      const newBalance = currentBalance + parseFloat(amount);
      sheet.getRange(i + 1, balanceIndex + 1).setValue(newBalance);
      return;
    }
  }
}

/**
 * 獲取系統設定
 */
function getSystemSettings() {
  const sheet = getSheet(SHEETS.SYSTEM_SETTINGS);
  const data = sheet.getDataRange().getValues();
  
  if (data.length <= 1) return {};
  
  const settings = {};
  for (let i = 1; i < data.length; i++) {
    const key = data[i][0];
    const value = data[i][1];
    
    // 嘗試解析 JSON
    try {
      settings[key] = JSON.parse(value);
    } catch (e) {
      settings[key] = value;
    }
  }
  
  return settings;
}

// ==================== 輔助函數 ====================

/**
 * 獲取工作表
 */
function getSheet(sheetName) {
  const spreadsheet = SpreadsheetApp.openById(SPREADSHEET_ID);
  const sheet = spreadsheet.getSheetByName(sheetName);
  
  if (!sheet) {
    throw new Error(`Sheet "${sheetName}" not found`);
  }
  
  return sheet;
}

/**
 * 建立回應
 */
function createResponse(data, success = true) {
  const response = {
    success: success,
    data: data,
    timestamp: new Date().toISOString()
  };
  
  return ContentService
    .createTextOutput(JSON.stringify(response))
    .setMimeType(ContentService.MimeType.JSON);
}

// ==================== 測試函數 ====================

/**
 * 測試連接
 */
function testConnection() {
  try {
    const spreadsheet = SpreadsheetApp.openById(SPREADSHEET_ID);
    const sheets = spreadsheet.getSheets();
    
    console.log('✅ 連接成功');
    console.log('📊 試算表名稱:', spreadsheet.getName());
    console.log('📋 工作表數量:', sheets.length);
    
    sheets.forEach(sheet => {
      console.log(`   - ${sheet.getName()}: ${sheet.getLastRow()} 行`);
    });
    
    return true;
  } catch (error) {
    console.error('❌ 連接失敗:', error.message);
    return false;
  }
}

/**
 * 初始化範例資料
 */
function initSampleData() {
  try {
    // 初始化用戶資料
    const userSheet = getSheet(SHEETS.USERS);
    if (userSheet.getLastRow() <= 1) {
      const sampleUsers = [
        ['admin_001', '管理員', '<EMAIL>', 'admin123', 'admin', 'ciguang', true, 0, 0, '{}'],
        ['super_001', '主管', '<EMAIL>', 'super123', 'supervisor', 'ciguang', true, 8, 0, '{}'],
        ['emp_001', '員工', '<EMAIL>', 'emp123', 'employee', 'ruiguang', true, 10, 0, '{}']
      ];
      
      sampleUsers.forEach(user => userSheet.appendRow(user));
      console.log('✅ 已初始化用戶資料');
    }
    
    // 初始化系統設定
    const settingsSheet = getSheet(SHEETS.SYSTEM_SETTINGS);
    if (settingsSheet.getLastRow() <= 1) {
      const sampleSettings = [
        ['companyName', '工作排班管理系統', new Date().toISOString()],
        ['workLocations', JSON.stringify([
          {id: 'ciguang', name: '慈光', minStaff: 2},
          {id: 'ruiguang', name: '瑞光', minStaff: 1}
        ]), new Date().toISOString()]
      ];
      
      sampleSettings.forEach(setting => settingsSheet.appendRow(setting));
      console.log('✅ 已初始化系統設定');
    }
    
    console.log('🎉 範例資料初始化完成');
    return true;
  } catch (error) {
    console.error('❌ 初始化失敗:', error.message);
    return false;
  }
}
