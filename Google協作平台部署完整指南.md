# Google 協作平台部署完整指南

## 🎯 部署目標
在 Google 協作平台上部署 workdays 專案，使用 Google Sheets 作為資料庫

## 📋 完整部署流程

### **第一階段：建立 Google Sheets 資料庫**

#### **步驟 1.1：建立 Google Sheets**
1. 前往 https://sheets.google.com
2. 建立新試算表，命名為 `workdays-database`
3. 按照 `Google_Sheets_簡易資料庫建立指南.md` 建立 4 個工作表
4. 記錄 Sheets ID（從 URL 中複製）

#### **步驟 1.2：設定共享權限**
1. 點擊「共用」按鈕
2. 設定為「知道連結的任何人都可以檢視」
3. 或新增特定使用者為「編輯者」

### **第二階段：建立 Google Apps Script API**

#### **步驟 2.1：建立 Apps Script 專案**
1. 前往 https://script.google.com
2. 點擊「新增專案」
3. 重新命名為 `workdays-api`

#### **步驟 2.2：設定 API 程式碼**
1. 刪除預設的 `myFunction`
2. 複製 `簡化版_Google_Apps_Script_API.js` 的完整內容
3. 貼上到編輯器中
4. **重要**：修改第 7 行的 SPREADSHEET_ID：
   ```javascript
   const SPREADSHEET_ID = '您的實際_SHEETS_ID';
   ```

#### **步驟 2.3：測試和授權**
1. 儲存程式碼（Ctrl+S）
2. 選擇函數 `testConnection`
3. 點擊「執行」
4. 授權所有必要權限
5. 確認執行日誌顯示連接成功

#### **步驟 2.4：初始化範例資料**
1. 選擇函數 `initSampleData`
2. 點擊「執行」
3. 確認 Google Sheets 中出現範例資料

#### **步驟 2.5：部署為 Web App**
1. 點擊「部署」→「新增部署作業」
2. 類型：「網頁應用程式」
3. 說明：`workdays API v1.0`
4. 執行身分：「我」
5. 存取權：「任何人」
6. 點擊「部署」
7. **複製 Web App URL**（重要！）

### **第三階段：修改前端程式碼**

#### **步驟 3.1：備份原始檔案**
```bash
cp schedule-app-simple.html schedule-app-simple-backup.html
```

#### **步驟 3.2：整合 Google Sheets API**
1. 開啟 `schedule-app-simple.html`
2. 在 `<script>` 標籤開始處加入 `Google_Sheets_前端整合程式碼.js` 的內容
3. **重要**：修改第 8 行的 API URL：
   ```javascript
   const GOOGLE_APPS_SCRIPT_URL = '您的實際_WEB_APP_URL';
   ```

#### **步驟 3.3：替換關鍵函數**
在 `schedule-app-simple.html` 中進行以下替換：

1. **替換 init 函數**：
   ```javascript
   // 原本的 init() 改為：
   function init() {
       initWithSheets();
   }
   ```

2. **替換登入函數**：
   ```javascript
   // 原本的 handleLogin 改為：
   function handleLogin(event) {
       return handleLoginWithSheets(event);
   }
   ```

3. **替換資料載入函數**：
   ```javascript
   // 原本的 loadSystemData 改為：
   function loadSystemData() {
       return loadSystemDataFromSheets();
   }
   ```

### **第四階段：部署到 Google 協作平台**

#### **步驟 4.1：建立 Google 協作平台**
1. 前往 https://sites.google.com
2. 點擊「建立新網站」
3. 選擇「空白」範本
4. 網站名稱：`工作排班管理系統`

#### **步驟 4.2：上傳系統檔案**
1. 點擊「插入」→「嵌入程式碼」
2. 選擇「上傳檔案」
3. 上傳修改後的 `schedule-app-simple.html`
4. 設定為全螢幕顯示

#### **步驟 4.3：設定頁面**
1. 編輯頁面標題：`工作排班管理系統`
2. 調整頁面佈局為全寬
3. 確認嵌入的 HTML 正常顯示

#### **步驟 4.4：設定網站權限**
1. 點擊右上角「分享」
2. 設定檢視權限：
   - **內部使用**：限制為組織內成員
   - **公開使用**：任何人都可檢視
3. 複製網站連結

#### **步驟 4.5：發布網站**
1. 點擊右上角「發布」
2. 選擇網址：`您的組織名稱-workdays`
3. 點擊「發布」
4. 記錄發布的網址

### **第五階段：測試和驗證**

#### **步驟 5.1：功能測試**
1. 開啟發布的網站
2. 測試登入功能（使用範例帳號）
3. 測試主要功能：
   - 排班申請
   - 審核流程
   - 額外班管理
   - 資料同步

#### **步驟 5.2：資料驗證**
1. 在系統中新增資料
2. 檢查 Google Sheets 是否同步更新
3. 重新載入頁面確認資料持久性

#### **步驟 5.3：多用戶測試**
1. 使用不同帳號登入
2. 測試權限控制
3. 確認資料隔離正確

## 🔧 設定檢查清單

### **Google Sheets 設定**
- [ ] 已建立 4 個工作表
- [ ] 每個工作表都有正確的標題行
- [ ] 已新增範例資料
- [ ] 已設定適當的共享權限
- [ ] 已記錄 Sheets ID

### **Google Apps Script 設定**
- [ ] 已設定正確的 SPREADSHEET_ID
- [ ] testConnection 函數執行成功
- [ ] initSampleData 函數執行成功
- [ ] 已部署為 Web App
- [ ] 已記錄 Web App URL

### **前端程式碼設定**
- [ ] 已整合 Google Sheets API 程式碼
- [ ] 已設定正確的 GOOGLE_APPS_SCRIPT_URL
- [ ] 已替換關鍵函數
- [ ] 本地測試正常

### **Google 協作平台設定**
- [ ] 已建立網站
- [ ] 已上傳修改後的 HTML 檔案
- [ ] 已設定適當的權限
- [ ] 已發布網站
- [ ] 已記錄網站 URL

### **功能測試**
- [ ] 登入功能正常
- [ ] 資料載入正常
- [ ] 排班申請功能正常
- [ ] 審核功能正常
- [ ] 額外班管理正常
- [ ] Google Sheets 同步正常

## 🚨 常見問題解決

### **API 連接失敗**
1. 檢查 Web App URL 是否正確
2. 確認 Apps Script 部署設定
3. 檢查瀏覽器控制台錯誤

### **權限錯誤**
1. 重新授權 Apps Script
2. 檢查 Google Sheets 共享設定
3. 確認 Web App 存取權設定

### **資料不同步**
1. 檢查 Sheets ID 是否正確
2. 確認工作表名稱拼寫
3. 檢查 API 呼叫是否成功

## 📞 技術支援

如果遇到問題，請提供：
1. 具體的錯誤訊息
2. 瀏覽器控制台日誌
3. Apps Script 執行日誌
4. 相關的設定截圖

## 🎉 部署完成

完成所有步驟後，您將擁有：
- ☁️ 雲端資料庫（Google Sheets）
- 🌐 Web API（Google Apps Script）
- 📱 前端應用（Google 協作平台）
- 🔒 完整的權限控制
- 📊 即時資料同步

**恭喜您成功部署了完整的雲端工作排班管理系統！** 🚀
