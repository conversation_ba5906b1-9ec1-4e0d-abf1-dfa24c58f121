/**
 * 工作排班管理系統 - Google Apps Script 後端 (修復版)
 * 版本：v1.1 - 修復 postData 錯誤
 * 作者：Augment Agent
 * 日期：2024-12
 */

// ==================== 配置設定 ====================

// Google Sheets ID - 請替換為您的實際 Sheets ID
const SPREADSHEET_ID = '1e0d0c62-f458-4f77-921c-e0bbf0ff2396';

// 工作表名稱
const SHEET_NAMES = {
  USERS: 'Users',
  SCHEDULE_REQUESTS: 'ScheduleRequests',
  OVERTIME_RECORDS: 'OvertimeRecords',
  NOTIFICATIONS: 'Notifications',
  EMPLOYEE_SCHEDULES: 'EmployeeSchedules',
  SHIFT_CHANGES: 'ShiftChanges',
  SYSTEM_SETTINGS: 'SystemSettings',
  ACTIVITY_LOGS: 'ActivityLogs'
};

// API 回應格式
const API_RESPONSE = {
  success: (data, message = 'Success') => ({
    success: true,
    message: message,
    data: data,
    timestamp: new Date().toISOString()
  }),
  error: (message, code = 'ERROR') => ({
    success: false,
    error: code,
    message: message,
    timestamp: new Date().toISOString()
  })
};

// ==================== 主要 API 端點 ====================

/**
 * 主要 POST 處理函數 - 修復版
 */
function doPost(e) {
  // 設定 CORS 標頭
  const response = ContentService.createTextOutput();
  response.setMimeType(ContentService.MimeType.JSON);
  
  try {
    console.log('收到 POST 請求:', e);
    
    // 檢查請求是否存在
    if (!e) {
      console.error('請求物件不存在');
      return response.setContent(JSON.stringify(
        API_RESPONSE.error('No request object', 'NO_REQUEST')
      ));
    }

    // 檢查 postData 是否存在
    if (!e.postData) {
      console.error('postData 不存在');
      return response.setContent(JSON.stringify(
        API_RESPONSE.error('Missing postData - please use POST method with JSON body', 'MISSING_POSTDATA')
      ));
    }

    // 檢查 contents 是否存在
    if (!e.postData.contents) {
      console.error('postData.contents 不存在');
      return response.setContent(JSON.stringify(
        API_RESPONSE.error('Missing postData contents', 'MISSING_CONTENTS')
      ));
    }

    console.log('postData.contents:', e.postData.contents);

    // 解析 JSON 資料
    let requestData;
    try {
      requestData = JSON.parse(e.postData.contents);
      console.log('解析後的請求資料:', requestData);
    } catch (parseError) {
      console.error('JSON 解析錯誤:', parseError);
      return response.setContent(JSON.stringify(
        API_RESPONSE.error('Invalid JSON format: ' + parseError.message, 'INVALID_JSON')
      ));
    }

    // 檢查必要欄位
    if (!requestData.action) {
      return response.setContent(JSON.stringify(
        API_RESPONSE.error('Missing action field', 'MISSING_ACTION')
      ));
    }

    const { action, data, auth } = requestData;
    console.log('處理動作:', action);

    // 記錄 API 請求
    try {
      logActivity('API_REQUEST', auth?.userId || 'anonymous', 'API', {
        action: action,
        timestamp: new Date().toISOString(),
        hasAuth: !!auth
      });
    } catch (logError) {
      console.warn('記錄活動失敗:', logError);
    }

    // 路由到對應的處理函數
    let result;
    switch (action) {
      // 測試相關
      case 'ping':
        result = handlePing();
        break;
      case 'testConnection':
        result = handleTestConnection();
        break;

      // 用戶相關
      case 'getUsers':
        result = handleGetUsers(auth);
        break;
      case 'createUser':
        result = handleCreateUser(data, auth);
        break;
      case 'authenticateUser':
        result = handleAuthenticateUser(data);
        break;

      // 排班申請相關
      case 'getScheduleRequests':
        result = handleGetScheduleRequests(data, auth);
        break;
      case 'createScheduleRequest':
        result = handleCreateScheduleRequest(data, auth);
        break;

      // 系統設定相關
      case 'getSystemSettings':
        result = handleGetSystemSettings(auth);
        break;
      case 'updateSystemSettings':
        result = handleUpdateSystemSettings(data, auth);
        break;

      default:
        result = API_RESPONSE.error('Unknown action: ' + action, 'UNKNOWN_ACTION');
    }

    return response.setContent(JSON.stringify(result));

  } catch (error) {
    console.error('API 處理錯誤:', error);
    return response.setContent(JSON.stringify(
      API_RESPONSE.error('Internal server error: ' + error.message, 'INTERNAL_ERROR')
    ));
  }
}

/**
 * GET 請求處理 - 修復版
 */
function doGet(e) {
  try {
    console.log('收到 GET 請求:', e);
    
    const action = e && e.parameter ? e.parameter.action : 'health';
    console.log('GET 動作:', action);
    
    let result;
    switch (action) {
      case 'health':
        result = API_RESPONSE.success({
          status: 'healthy',
          version: '1.1',
          timestamp: new Date().toISOString(),
          sheetsId: SPREADSHEET_ID,
          message: 'Google Apps Script is running normally'
        });
        break;
        
      case 'test':
        result = handleTestConnection();
        break;
        
      case 'ping':
        result = handlePing();
        break;
        
      default:
        result = API_RESPONSE.error(
          'Invalid GET action. Available: health, test, ping', 
          'INVALID_GET_ACTION'
        );
    }
    
    return ContentService
      .createTextOutput(JSON.stringify(result))
      .setMimeType(ContentService.MimeType.JSON);
      
  } catch (error) {
    console.error('GET 請求錯誤:', error);
    return ContentService
      .createTextOutput(JSON.stringify(
        API_RESPONSE.error('GET request failed: ' + error.message, 'GET_ERROR')
      ))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

// ==================== 處理函數 ====================

/**
 * Ping 測試
 */
function handlePing() {
  return API_RESPONSE.success({
    message: 'pong',
    timestamp: new Date().toISOString(),
    sheetsId: SPREADSHEET_ID
  });
}

/**
 * 測試連接
 */
function handleTestConnection() {
  try {
    console.log('開始測試連接...');
    
    // 測試 Sheets 連接
    const spreadsheet = SpreadsheetApp.openById(SPREADSHEET_ID);
    console.log('成功開啟 Spreadsheet');
    
    const sheets = spreadsheet.getSheets();
    console.log('取得工作表列表，數量:', sheets.length);
    
    const sheetInfo = sheets.map(sheet => ({
      name: sheet.getName(),
      rows: sheet.getLastRow(),
      cols: sheet.getLastColumn()
    }));
    
    return API_RESPONSE.success({
      message: 'Connection test successful',
      sheetsId: spreadsheet.getId(),
      sheetsName: spreadsheet.getName(),
      sheetsCount: sheets.length,
      sheets: sheetInfo,
      testTime: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('連接測試失敗:', error);
    return API_RESPONSE.error(
      'Connection test failed: ' + error.message, 
      'CONNECTION_FAILED'
    );
  }
}

/**
 * 用戶認證
 */
function handleAuthenticateUser(data) {
  try {
    console.log('處理用戶認證...');
    
    if (!data || !data.email || !data.password) {
      return API_RESPONSE.error('Missing email or password', 'MISSING_CREDENTIALS');
    }
    
    // 這裡先返回測試用戶，實際應該查詢 Sheets
    const testUser = {
      id: 'admin_001',
      name: '系統管理員',
      email: data.email,
      role: 'admin',
      location: 'ciguang'
    };
    
    const token = 'token_' + Date.now();
    
    return API_RESPONSE.success({
      user: testUser,
      token: token
    });
    
  } catch (error) {
    console.error('認證失敗:', error);
    return API_RESPONSE.error('Authentication failed: ' + error.message, 'AUTH_FAILED');
  }
}

/**
 * 獲取用戶列表
 */
function handleGetUsers(auth) {
  try {
    console.log('獲取用戶列表...');
    
    // 簡單的權限檢查
    if (!auth || !auth.userId) {
      return API_RESPONSE.error('Authentication required', 'AUTH_REQUIRED');
    }
    
    // 返回測試資料
    const users = [
      {
        id: 'admin_001',
        name: '系統管理員',
        email: '<EMAIL>',
        role: 'admin',
        location: 'ciguang'
      }
    ];
    
    return API_RESPONSE.success(users);
    
  } catch (error) {
    console.error('獲取用戶失敗:', error);
    return API_RESPONSE.error('Get users failed: ' + error.message, 'GET_USERS_FAILED');
  }
}

/**
 * 建立用戶
 */
function handleCreateUser(data, auth) {
  try {
    console.log('建立用戶...');
    
    if (!auth || !auth.userId) {
      return API_RESPONSE.error('Authentication required', 'AUTH_REQUIRED');
    }
    
    if (!data || !data.name || !data.email) {
      return API_RESPONSE.error('Missing required fields', 'MISSING_FIELDS');
    }
    
    // 建立新用戶（這裡是測試版本）
    const newUser = {
      id: 'user_' + Date.now(),
      name: data.name,
      email: data.email,
      role: data.role || 'employee',
      location: data.location || 'ciguang',
      approved: true,
      createdAt: new Date().toISOString()
    };
    
    return API_RESPONSE.success(newUser, 'User created successfully');
    
  } catch (error) {
    console.error('建立用戶失敗:', error);
    return API_RESPONSE.error('Create user failed: ' + error.message, 'CREATE_USER_FAILED');
  }
}

/**
 * 獲取排班申請
 */
function handleGetScheduleRequests(data, auth) {
  try {
    console.log('獲取排班申請...');
    
    if (!auth || !auth.userId) {
      return API_RESPONSE.error('Authentication required', 'AUTH_REQUIRED');
    }
    
    // 返回測試資料
    const requests = [];
    
    return API_RESPONSE.success(requests);
    
  } catch (error) {
    console.error('獲取排班申請失敗:', error);
    return API_RESPONSE.error('Get schedule requests failed: ' + error.message, 'GET_REQUESTS_FAILED');
  }
}

/**
 * 建立排班申請
 */
function handleCreateScheduleRequest(data, auth) {
  try {
    console.log('建立排班申請...');
    
    if (!auth || !auth.userId) {
      return API_RESPONSE.error('Authentication required', 'AUTH_REQUIRED');
    }
    
    if (!data || !data.month || !data.scheduleData) {
      return API_RESPONSE.error('Missing required fields', 'MISSING_FIELDS');
    }
    
    // 建立新申請（測試版本）
    const newRequest = {
      id: 'req_' + Date.now(),
      employeeId: auth.userId,
      month: data.month,
      status: 'pending',
      scheduleData: data.scheduleData,
      submittedAt: new Date().toISOString()
    };
    
    return API_RESPONSE.success(newRequest, 'Schedule request created successfully');
    
  } catch (error) {
    console.error('建立排班申請失敗:', error);
    return API_RESPONSE.error('Create schedule request failed: ' + error.message, 'CREATE_REQUEST_FAILED');
  }
}

/**
 * 獲取系統設定
 */
function handleGetSystemSettings(auth) {
  try {
    console.log('獲取系統設定...');
    
    // 返回預設設定
    const settings = {
      companyName: '工作排班管理系統',
      workLocations: [
        { id: 'ciguang', name: '慈光', minStaff: 2 },
        { id: 'ruiguang', name: '瑞光', minStaff: 1 }
      ],
      shiftTypes: [
        { id: 'early', name: '早班', startTime: '09:00', endTime: '17:00' },
        { id: 'mid', name: '中班', startTime: '13:00', endTime: '21:00' }
      ]
    };
    
    return API_RESPONSE.success(settings);
    
  } catch (error) {
    console.error('獲取系統設定失敗:', error);
    return API_RESPONSE.error('Get system settings failed: ' + error.message, 'GET_SETTINGS_FAILED');
  }
}

/**
 * 更新系統設定
 */
function handleUpdateSystemSettings(data, auth) {
  try {
    console.log('更新系統設定...');
    
    if (!auth || !auth.userId) {
      return API_RESPONSE.error('Authentication required', 'AUTH_REQUIRED');
    }
    
    // 這裡應該實際更新 Sheets，目前返回成功
    return API_RESPONSE.success(data, 'System settings updated successfully');
    
  } catch (error) {
    console.error('更新系統設定失敗:', error);
    return API_RESPONSE.error('Update system settings failed: ' + error.message, 'UPDATE_SETTINGS_FAILED');
  }
}

// ==================== 輔助函數 ====================

/**
 * 記錄活動日誌
 */
function logActivity(action, userId, userName, details) {
  try {
    console.log('記錄活動:', action, userId, details);
    // 這裡應該寫入 ActivityLogs 工作表
    // 目前只記錄到控制台
  } catch (error) {
    console.error('記錄活動失敗:', error);
  }
}
