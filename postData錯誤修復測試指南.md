# postData 錯誤修復測試指南

## 🚨 錯誤分析

**錯誤訊息**：`Invalid request: missing postData`

**原因**：Google Apps Script 沒有收到正確的 POST 請求資料

## 🔧 修復方案

我已經建立了修復版的 Google Apps Script 後端，包含：

### **1. 增強的錯誤檢查**
- ✅ 檢查請求物件是否存在
- ✅ 檢查 postData 是否存在
- ✅ 檢查 postData.contents 是否存在
- ✅ 詳細的錯誤訊息和日誌

### **2. 改善的請求處理**
- ✅ 更好的 JSON 解析錯誤處理
- ✅ CORS 標頭設定
- ✅ 完整的日誌記錄

### **3. 測試端點**
- ✅ GET /health - 健康檢查
- ✅ GET /test - 連接測試
- ✅ POST /ping - 簡單 ping 測試

## 🚀 立即修復步驟

### **步驟 1：更新 Google Apps Script**

1. **開啟您的 Google Apps Script 專案**
   - 前往：https://script.google.com
   - 開啟您的專案

2. **完全替換程式碼**
   - 選取所有現有程式碼（Ctrl+A）
   - 刪除現有程式碼
   - 複製 `修復版_Google_Apps_Script_後端.js` 的完整內容
   - 貼上新程式碼

3. **確認 Sheets ID**
   - 檢查第 11 行：
   ```javascript
   const SPREADSHEET_ID = '1e0d0c62-f458-4f77-921c-e0bbf0ff2396';
   ```
   - 確認這是您的正確 Sheets ID

4. **儲存程式碼**
   - 按 Ctrl+S 儲存
   - 確認沒有語法錯誤

### **步驟 2：測試程式碼**

1. **執行測試函數**
   - 在函數下拉選單選擇：`handleTestConnection`
   - 點擊「執行」
   - 檢查執行日誌

2. **授權權限**
   - 如果提示需要授權，點擊「授權」
   - 允許訪問 Google Sheets

### **步驟 3：重新部署**

1. **建立新部署**
   - 點擊「部署」→「新增部署作業」
   - 類型：「網頁應用程式」
   - 說明：`修復 postData 錯誤 v1.1`
   - 執行身分：「我」
   - 存取權：「任何人」

2. **取得新 URL**
   - 點擊「部署」
   - **複製新的 Web App URL**
   - 格式類似：`https://script.google.com/macros/s/AKfycby.../exec`

### **步驟 4：測試修復結果**

#### **測試 1：GET 健康檢查**
在瀏覽器中訪問：
```
您的_WEB_APP_URL?action=health
```

**預期結果**：
```json
{
  "success": true,
  "message": "Success",
  "data": {
    "status": "healthy",
    "version": "1.1",
    "timestamp": "2024-12-01T10:00:00.000Z",
    "sheetsId": "1e0d0c62-f458-4f77-921c-e0bbf0ff2396",
    "message": "Google Apps Script is running normally"
  },
  "timestamp": "2024-12-01T10:00:00.000Z"
}
```

#### **測試 2：GET 連接測試**
在瀏覽器中訪問：
```
您的_WEB_APP_URL?action=test
```

**預期結果**：
```json
{
  "success": true,
  "message": "Success",
  "data": {
    "message": "Connection test successful",
    "sheetsId": "1e0d0c62-f458-4f77-921c-e0bbf0ff2396",
    "sheetsName": "工作排班管理系統",
    "sheetsCount": 8,
    "sheets": [...],
    "testTime": "2024-12-01T10:00:00.000Z"
  }
}
```

#### **測試 3：POST ping 測試**
在瀏覽器控制台執行：
```javascript
fetch('您的_WEB_APP_URL', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    action: 'ping'
  })
})
.then(response => response.json())
.then(data => console.log('✅ POST ping 成功:', data))
.catch(error => console.error('❌ POST ping 失敗:', error));
```

**預期結果**：
```json
{
  "success": true,
  "message": "Success",
  "data": {
    "message": "pong",
    "timestamp": "2024-12-01T10:00:00.000Z",
    "sheetsId": "1e0d0c62-f458-4f77-921c-e0bbf0ff2396"
  }
}
```

#### **測試 4：POST 認證測試**
```javascript
fetch('您的_WEB_APP_URL', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    action: 'authenticateUser',
    data: {
      email: '<EMAIL>',
      password: 'admin123'
    }
  })
})
.then(response => response.json())
.then(data => console.log('✅ 認證測試成功:', data))
.catch(error => console.error('❌ 認證測試失敗:', error));
```

## 🔍 故障排除

### **如果仍然出現 "missing postData" 錯誤**

1. **檢查請求方法**
   - 確認使用 POST 方法
   - 確認設定 Content-Type: application/json

2. **檢查請求格式**
   ```javascript
   // 正確格式
   {
     "action": "ping",
     "data": null,
     "auth": null
   }
   ```

3. **檢查 Apps Script 部署設定**
   - 執行身分：「我」
   - 存取權：「任何人」

### **如果出現權限錯誤**

1. **重新授權**
   - 在 Apps Script 中執行任一函數
   - 重新授權所有權限

2. **檢查 Sheets 權限**
   - 確認 Apps Script 可以訪問您的 Google Sheets
   - 確認 Sheets ID 正確

### **如果出現 CORS 錯誤**

1. **檢查前端 URL**
   - 確認前端使用 HTTPS
   - 確認 Web App URL 正確

2. **檢查瀏覽器設定**
   - 暫時停用廣告攔截器
   - 清除瀏覽器快取

## 📋 測試檢查清單

完成修復後，請確認：

- [ ] **GET /health** 回應正常
- [ ] **GET /test** 回應正常，顯示 Sheets 資訊
- [ ] **POST /ping** 回應正常
- [ ] **POST /authenticateUser** 回應正常
- [ ] Apps Script 執行日誌沒有錯誤
- [ ] 可以在瀏覽器控制台成功呼叫 API

## 🎯 下一步

修復完成並測試通過後：

1. **更新前端設定**
   ```javascript
   const GOOGLE_APPS_SCRIPT_URL = '您的新_WEB_APP_URL';
   ```

2. **整合前端程式碼**
   - 將 Google Sheets 整合程式碼加入現有系統
   - 替換 localStorage 函數為 API 呼叫

3. **執行資料遷移**
   - 使用資料遷移腳本
   - 將現有資料遷移到 Google Sheets

## 💬 需要協助？

如果修復後仍有問題，請提供：

1. **測試結果**：每個測試的具體結果
2. **錯誤訊息**：完整的錯誤日誌
3. **Apps Script 日誌**：執行日誌的截圖或內容
4. **網路請求詳情**：瀏覽器開發者工具中的請求/回應資訊

我會協助您進一步診斷和解決問題！

---

**🚀 準備好開始修復了嗎？**

請按照步驟操作，並告訴我每個測試的結果！
